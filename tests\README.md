# Test Suite

This directory contains tests for the AI Agent System.

## Test Files

### Unit Tests
- `test_function_calling.py` - Tests function calling functionality including registration, execution, and AI integration
- `test_embedding.py` - Tests embedding service functionality
- `test_azure_openai_provider.py` - Tests Azure OpenAI provider implementation
- `test_azure_openai_integration.py` - Tests Azure OpenAI integration

### Integration Tests
- `test_api.py` - Tests API endpoints for authentication and session management
- `test_complete_system.py` - Comprehensive system test covering all phases

## Running Tests

### Prerequisites
1. Ensure the server is running: `docker-compose up --build -d`
2. Configure environment variables in `.env`

### Running Individual Tests
```bash
# Function calling tests
python tests/test_function_calling.py

# API integration tests
python tests/test_api.py

# Complete system test
python tests/test_complete_system.py

# Embedding tests
python tests/test_embedding.py
```

### Test Coverage
- ✅ Function calling and execution
- ✅ LLM provider integration
- ✅ Agent creation and management
- ✅ API endpoints
- ✅ Session management
- ✅ WebSocket communication
- ✅ Embedding services

## Test Data
Tests use temporary data and should not affect production databases.
