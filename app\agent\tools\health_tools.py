"""
Health and medical calculation tools for AI agents.

This module contains tools for health-related calculations and information.
"""

import logging
import math
from typing import Dict, Any, List

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry

logger = logging.getLogger(__name__)


class HealthCalculatorTool(BaseTool):
    """Tool for health-related calculations."""
    
    @property
    def name(self) -> str:
        return "health_calculator"
    
    @property
    def description(self) -> str:
        return "Perform health-related calculations like BMI, BMR, and other medical metrics"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "calculation_type": {
                        "type": "string",
                        "description": "Type of health calculation",
                        "enum": ["bmi", "bmr", "body_fat", "ideal_weight", "calorie_needs"],
                        "default": "bmi"
                    },
                    "weight": {
                        "type": "number",
                        "description": "Weight in kilograms"
                    },
                    "height": {
                        "type": "number",
                        "description": "Height in centimeters"
                    },
                    "age": {
                        "type": "integer",
                        "description": "Age in years"
                    },
                    "gender": {
                        "type": "string",
                        "description": "Gender for calculations that require it",
                        "enum": ["male", "female"]
                    },
                    "activity_level": {
                        "type": "string",
                        "description": "Activity level for calorie calculations",
                        "enum": ["sedentary", "light", "moderate", "active", "very_active"],
                        "default": "moderate"
                    }
                },
                "required": ["calculation_type"]
            }
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Perform health calculations.
        
        Args:
            calculation_type: Type of calculation to perform
            weight: Weight in kg
            height: Height in cm
            age: Age in years
            gender: Gender
            activity_level: Activity level
            
        Returns:
            ToolResult with calculation results
        """
        try:
            calc_type = kwargs.get("calculation_type", "bmi")
            weight = kwargs.get("weight")
            height = kwargs.get("height")
            age = kwargs.get("age")
            gender = kwargs.get("gender")
            activity_level = kwargs.get("activity_level", "moderate")
            
            if calc_type == "bmi":
                return await self._calculate_bmi(weight, height)
            elif calc_type == "bmr":
                return await self._calculate_bmr(weight, height, age, gender)
            elif calc_type == "ideal_weight":
                return await self._calculate_ideal_weight(height, gender)
            elif calc_type == "calorie_needs":
                return await self._calculate_calorie_needs(weight, height, age, gender, activity_level)
            else:
                return ToolResult(
                    success=False,
                    error=f"Unsupported calculation type: {calc_type}"
                )
                
        except Exception as e:
            logger.error(f"Health calculation error: {str(e)}")
            return ToolResult(
                success=False,
                error=f"Health calculation failed: {str(e)}"
            )
    
    async def _calculate_bmi(self, weight: float, height: float) -> ToolResult:
        """Calculate Body Mass Index."""
        if not weight or not height:
            return ToolResult(
                success=False,
                error="Weight and height are required for BMI calculation"
            )
        
        if weight <= 0 or height <= 0:
            return ToolResult(
                success=False,
                error="Weight and height must be positive values"
            )
        
        # Convert height from cm to meters
        height_m = height / 100
        bmi = weight / (height_m ** 2)
        
        # Determine BMI category
        if bmi < 18.5:
            category = "Underweight"
        elif bmi < 25:
            category = "Normal weight"
        elif bmi < 30:
            category = "Overweight"
        else:
            category = "Obese"
        
        return ToolResult(
            success=True,
            result={
                "bmi": round(bmi, 2),
                "category": category,
                "weight_kg": weight,
                "height_cm": height,
                "height_m": height_m
            },
            metadata={
                "calculation": "bmi",
                "unit": "kg/m²"
            }
        )
    
    async def _calculate_bmr(self, weight: float, height: float, age: int, gender: str) -> ToolResult:
        """Calculate Basal Metabolic Rate using Mifflin-St Jeor equation."""
        if not all([weight, height, age, gender]):
            return ToolResult(
                success=False,
                error="Weight, height, age, and gender are required for BMR calculation"
            )
        
        if weight <= 0 or height <= 0 or age <= 0:
            return ToolResult(
                success=False,
                error="Weight, height, and age must be positive values"
            )
        
        # Mifflin-St Jeor equation
        if gender.lower() == "male":
            bmr = 10 * weight + 6.25 * height - 5 * age + 5
        elif gender.lower() == "female":
            bmr = 10 * weight + 6.25 * height - 5 * age - 161
        else:
            return ToolResult(
                success=False,
                error="Gender must be 'male' or 'female'"
            )
        
        return ToolResult(
            success=True,
            result={
                "bmr": round(bmr, 2),
                "unit": "calories/day",
                "formula": "Mifflin-St Jeor equation",
                "weight_kg": weight,
                "height_cm": height,
                "age": age,
                "gender": gender
            },
            metadata={
                "calculation": "bmr",
                "formula": "mifflin_st_jeor"
            }
        )
    
    async def _calculate_ideal_weight(self, height: float, gender: str) -> ToolResult:
        """Calculate ideal weight using Devine formula."""
        if not height or not gender:
            return ToolResult(
                success=False,
                error="Height and gender are required for ideal weight calculation"
            )
        
        if height <= 0:
            return ToolResult(
                success=False,
                error="Height must be a positive value"
            )
        
        # Devine formula
        height_inches = height / 2.54  # Convert cm to inches
        
        if gender.lower() == "male":
            ideal_weight = 50 + 2.3 * (height_inches - 60)
        elif gender.lower() == "female":
            ideal_weight = 45.5 + 2.3 * (height_inches - 60)
        else:
            return ToolResult(
                success=False,
                error="Gender must be 'male' or 'female'"
            )
        
        # Ensure minimum weight
        ideal_weight = max(ideal_weight, 40)
        
        return ToolResult(
            success=True,
            result={
                "ideal_weight_kg": round(ideal_weight, 2),
                "height_cm": height,
                "gender": gender,
                "formula": "Devine formula"
            },
            metadata={
                "calculation": "ideal_weight",
                "formula": "devine"
            }
        )
    
    async def _calculate_calorie_needs(self, weight: float, height: float, age: int, gender: str, activity_level: str) -> ToolResult:
        """Calculate daily calorie needs based on BMR and activity level."""
        # First calculate BMR
        bmr_result = await self._calculate_bmr(weight, height, age, gender)
        if not bmr_result.success:
            return bmr_result
        
        bmr = bmr_result.result["bmr"]
        
        # Activity multipliers
        activity_multipliers = {
            "sedentary": 1.2,
            "light": 1.375,
            "moderate": 1.55,
            "active": 1.725,
            "very_active": 1.9
        }
        
        multiplier = activity_multipliers.get(activity_level, 1.55)
        daily_calories = bmr * multiplier
        
        return ToolResult(
            success=True,
            result={
                "daily_calories": round(daily_calories, 2),
                "bmr": bmr,
                "activity_level": activity_level,
                "activity_multiplier": multiplier,
                "weight_kg": weight,
                "height_cm": height,
                "age": age,
                "gender": gender
            },
            metadata={
                "calculation": "calorie_needs",
                "activity_level": activity_level
            }
        )


# Register tools
tool_registry.register_tool(HealthCalculatorTool())
