import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from app.llm.base import <PERSON><PERSON>rovider, ChatMessage, FunctionCall, ToolCall
from app.llm.provider_factory import create_llm_provider
from app.agent.role_loader import role_loader, RoleConfig
from app.session.storage import session_storage
from app.services.function_executor import function_executor
from app.core.exceptions import ValidationError, LLMError
from app.core.config import settings

# Import sample functions to register them
import app.services.sample_functions

logger = logging.getLogger(__name__)


class AgentMemory:
    """Manages conversation memory for an agent session."""
    
    def __init__(self, session_key: str, max_messages: int = 50):
        self.session_key = session_key
        self.max_messages = max_messages
        self.messages: List[ChatMessage] = []
        self.system_message: Optional[ChatMessage] = None
    
    def set_system_message(self, content: str):
        """Set the system message for the agent."""
        self.system_message = ChatMessage(
            role="system",
            content=content
        )
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """Add a message to the conversation history."""
        message = ChatMessage(
            role=role,
            content=content,
            metadata=metadata or {}
        )
        
        self.messages.append(message)
        
        # Keep only the most recent messages
        if len(self.messages) > self.max_messages:
            self.messages = self.messages[-self.max_messages:]
    
    def get_conversation_messages(self) -> List[ChatMessage]:
        """Get all messages for the LLM, including system message."""
        conversation = []
        
        if self.system_message:
            conversation.append(self.system_message)
        
        conversation.extend(self.messages)
        return conversation
    
    def clear_history(self):
        """Clear conversation history (but keep system message)."""
        self.messages.clear()
    
    async def save_to_storage(self):
        """Save conversation history to storage."""
        try:
            messages_data = [
                {
                    "role": msg.role,
                    "content": msg.content,
                    "metadata": msg.metadata or {},
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                for msg in self.messages
            ]

            session_storage.save_session_messages(
                self.session_key,
                messages_data,
                max_messages=self.max_messages
            )
        except Exception as e:
            logger.error(f"Failed to save messages for session {self.session_key}: {str(e)}")

    async def load_from_storage(self):
        """Load conversation history from storage."""
        try:
            messages_data = session_storage.get_session_messages(self.session_key)

            self.messages.clear()
            for msg_data in messages_data:
                self.messages.append(ChatMessage(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    metadata=msg_data.get("metadata", {})
                ))
        except Exception as e:
            logger.error(f"Failed to load messages for session {self.session_key}: {str(e)}")
            # Continue with empty message history


class AIAgent:
    """AI Agent that combines a role configuration with an LLM provider."""
    
    def __init__(
        self,
        session_key: str,
        role_config: RoleConfig,
        llm_provider: LLMProvider,
        memory: AgentMemory
    ):
        self.session_key = session_key
        self.role_config = role_config
        self.llm_provider = llm_provider
        self.memory = memory
        
        # Set up system message
        self.memory.set_system_message(role_config.system_prompt)
    
    async def generate_response(self, user_message: str) -> str:
        """
        Generate a response to a user message with function calling support.

        Args:
            user_message: The user's input message

        Returns:
            str: The agent's response

        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)

            # Get conversation messages
            conversation = self.memory.get_conversation_messages()

            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate response using LLM with tools
            llm_config = self.role_config.config.copy()
            response = await self.llm_provider.generate_response(
                conversation,
                tools=tools,
                **llm_config
            )

            # Handle function calls if present
            final_response = await self._handle_function_calls(response, conversation, tools, llm_config)

            # Add final assistant response to memory
            self.memory.add_message(
                "assistant",
                final_response,
                metadata=response.metadata
            )

            # Save conversation to storage and update session metadata
            await self._update_session_state()

            return final_response

        except Exception as e:
            raise LLMError(f"Failed to generate response: {str(e)}")

    def _get_tools_for_role(self) -> List[Dict[str, Any]]:
        """Get available tools/functions for this role."""
        if not self.role_config.tools:
            return []

        # Get function schemas for the role's tools
        return function_executor.get_functions_for_tools(self.role_config.tools)

    async def _handle_function_calls(
        self,
        response,
        conversation: List[ChatMessage],
        tools: List[Dict[str, Any]],
        llm_config: Dict[str, Any]
    ) -> str:
        """Handle function calls in the LLM response."""
        # If no function calls, return the content directly
        if not response.tool_calls and not response.function_call:
            return response.content or ""

        # Collect function calls from both formats
        function_calls = []
        if response.function_call:
            function_calls.append(response.function_call)
        if response.tool_calls:
            function_calls.extend([tool_call.function for tool_call in response.tool_calls])

        # Execute all function calls
        function_results = []
        for func_call in function_calls:
            result = await function_executor.execute_function(func_call.name, func_call.arguments)
            function_results.append({
                "name": func_call.name,
                "result": result.result if result.success else f"Error: {result.error}",
                "success": result.success
            })

        # Build updated conversation with function calls and results
        updated_conversation = conversation.copy()

        # Add assistant message with function call(s)
        updated_conversation.append(ChatMessage(
            role="assistant",
            content=response.content,
            tool_calls=response.tool_calls,
            function_call=response.function_call
        ))

        # Add function/tool responses
        if response.tool_calls:
            # New tool call format
            for tool_call, func_result in zip(response.tool_calls, function_results):
                updated_conversation.append(ChatMessage(
                    role="tool",
                    content=json.dumps(func_result),
                    tool_call_id=tool_call.id,
                    name=func_result["name"]
                ))
        elif response.function_call:
            # Legacy function call format
            updated_conversation.append(ChatMessage(
                role="function",
                content=json.dumps(function_results[0]),
                name=function_results[0]["name"]
            ))

        # Get final response from LLM
        final_response = await self.llm_provider.generate_response(
            updated_conversation,
            tools=tools,
            **llm_config
        )

        return final_response.content or ""

    async def generate_streaming_response(self, user_message: str):
        """
        Generate a streaming response to a user message.
        
        Args:
            user_message: The user's input message
            
        Yields:
            str: Streaming response chunks
            
        Raises:
            LLMError: If response generation fails
        """
        try:
            # Add user message to memory
            self.memory.add_message("user", user_message)
            
            # Get conversation messages
            conversation = self.memory.get_conversation_messages()
            
            # Get available tools for this role
            tools = self._get_tools_for_role()

            # Generate streaming response with tools
            llm_config = self.role_config.config.copy()
            full_response = ""

            async for chunk in self.llm_provider.generate_stream(
                conversation,
                tools=tools,
                **llm_config
            ):
                if chunk.content:
                    full_response += chunk.content
                    yield chunk.content
                
                if chunk.is_complete:
                    break
            
            # Add complete assistant response to memory
            if full_response:
                self.memory.add_message(
                    "assistant", 
                    full_response,
                    metadata={"streaming": True}
                )
                
                # Save conversation to storage and update session metadata
                await self._update_session_state()
            
        except Exception as e:
            raise LLMError(f"Failed to generate streaming response: {str(e)}")

    async def _update_session_state(self):
        """Update session state in storage."""
        try:
            await self.memory.save_to_storage()
            session_storage.update_session_metadata(self.session_key, {
                "last_activity": datetime.now(timezone.utc).isoformat(),
                "message_count": len(self.memory.messages)
            })
        except Exception as e:
            logger.error(f"Failed to update session state for {self.session_key}: {str(e)}")

    def get_role_info(self) -> Dict[str, Any]:
        """Get information about the agent's role."""
        return {
            "role_name": self.role_config.display_name,
            "description": self.role_config.description,
            "tools": self.role_config.tools or [],
            "message_count": len(self.memory.messages)
        }


class AgentFactory:
    """Factory for creating AI agents with specific roles."""

    def __init__(self):
        self._active_agents: Dict[str, AIAgent] = {}
        self._role_cache: Dict[str, RoleConfig] = {}
    
    async def create_agent(
        self,
        session_key: str,
        role_name: str,
        llm_provider: Optional[LLMProvider] = None
    ) -> AIAgent:
        """
        Create or retrieve an AI agent for a session.
        
        Args:
            session_key: Unique session identifier
            role_name: Name of the role to assign
            llm_provider: Optional custom LLM provider
            
        Returns:
            AIAgent: Configured AI agent
            
        Raises:
            ValidationError: If role doesn't exist
            LLMError: If LLM provider setup fails
        """
        # Check if agent already exists for this session
        if session_key in self._active_agents:
            return self._active_agents[session_key]
        
        # Get role configuration (with caching)
        role_config = self._get_role_config(role_name)
        if not role_config or not role_config.is_active:
            raise ValidationError(f"Role '{role_name}' not found or inactive")
        
        # Create LLM provider if not provided
        if llm_provider is None:
            llm_provider = create_llm_provider(config=role_config.config)

        # Validate LLM connection (log warnings but don't fail agent creation)
        try:
            connection_valid = await llm_provider.validate_connection()
            if not connection_valid:
                logger.warning(f"LLM connection validation failed for session {session_key}")
        except Exception as e:
            logger.warning(f"LLM connection validation error for session {session_key}: {str(e)}")
        
        # Create memory manager
        memory = AgentMemory(
            session_key=session_key,
            max_messages=settings.max_messages_per_session
        )
        
        # Load existing conversation from storage
        await memory.load_from_storage()
        
        # Create agent
        agent = AIAgent(
            session_key=session_key,
            role_config=role_config,
            llm_provider=llm_provider,
            memory=memory
        )
        
        # Cache the agent
        self._active_agents[session_key] = agent
        
        return agent
    
    async def get_agent(self, session_key: str) -> Optional[AIAgent]:
        """Get an existing agent for a session."""
        return self._active_agents.get(session_key)
    
    async def remove_agent(self, session_key: str) -> bool:
        """Remove an agent from memory (usually when session is deleted)."""
        if session_key in self._active_agents:
            del self._active_agents[session_key]
            return True
        return False
    
    def get_active_sessions(self) -> List[str]:
        """Get list of session keys with active agents."""
        return list(self._active_agents.keys())
    
    def _get_role_config(self, role_name: str) -> Optional[RoleConfig]:
        """Get role configuration with caching."""
        if role_name not in self._role_cache:
            self._role_cache[role_name] = role_loader.get_role(role_name)
        return self._role_cache[role_name]

    def clear_role_cache(self):
        """Clear the role configuration cache."""
        self._role_cache.clear()

    async def cleanup_inactive_agents(self, active_session_keys: List[str]):
        """Remove agents for sessions that are no longer active."""
        current_sessions = set(self._active_agents.keys())
        active_sessions = set(active_session_keys)

        inactive_sessions = current_sessions - active_sessions

        for session_key in inactive_sessions:
            await self.remove_agent(session_key)

        logger.info(f"Cleaned up {len(inactive_sessions)} inactive agents")


# Global agent factory instance
agent_factory = AgentFactory()