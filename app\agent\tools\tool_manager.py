"""
Tool Manager for unified tool handling.

This module provides a unified interface for managing both BaseTool instances
and function-based tools, automatically discovering and registering tools.
"""

import os
import importlib
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from app.agent.tools.base import BaseTool, ToolRegistry, tool_registry
from app.agent.tools.function_executor import function_executor

logger = logging.getLogger(__name__)


class ToolManager:
    """Unified tool manager that handles both BaseTool and function-based tools."""
    
    def __init__(self):
        self.tool_registry = tool_registry
        self.function_executor = function_executor
        self._tools_loaded = False
    
    def discover_and_load_tools(self) -> None:
        """Discover and load all tools from the tools directory."""
        if self._tools_loaded:
            return
        
        tools_dir = Path(__file__).parent
        logger.info(f"Discovering tools in: {tools_dir}")
        
        # Import all Python files in the tools directory
        for file_path in tools_dir.glob("*.py"):
            if file_path.name.startswith("__") or file_path.name == "tool_manager.py":
                continue
            
            module_name = f"app.agent.tools.{file_path.stem}"
            try:
                importlib.import_module(module_name)
                logger.info(f"Loaded tool module: {module_name}")
            except Exception as e:
                logger.error(f"Failed to load tool module {module_name}: {str(e)}")
        
        self._tools_loaded = True
        logger.info(f"Tool discovery complete. Registered tools: {self.get_available_tool_names()}")
    
    def get_available_tool_names(self) -> List[str]:
        """Get all available tool names from both registries."""
        tool_names = set()
        
        # Get tools from tool registry
        tool_names.update(self.tool_registry.list_tools())
        
        # Get functions from function executor
        tool_names.update(self.function_executor.registry.list_functions())
        
        return list(tool_names)
    
    def get_tools_for_role(self, tool_names: List[str]) -> List[Dict[str, Any]]:
        """
        Get tool schemas for a specific role.
        
        Args:
            tool_names: List of tool names required by the role
            
        Returns:
            List of tool schemas in OpenAI function format
        """
        self.discover_and_load_tools()
        
        schemas = []
        missing_tools = []
        
        for tool_name in tool_names:
            schema = self._get_tool_schema(tool_name)
            if schema:
                schemas.append(schema)
            else:
                missing_tools.append(tool_name)
        
        if missing_tools:
            logger.warning(f"Missing tools for role: {missing_tools}")
        
        logger.info(f"Loaded {len(schemas)} tools for role: {[s['function']['name'] for s in schemas]}")
        return schemas
    
    def _get_tool_schema(self, tool_name: str) -> Optional[Dict[str, Any]]:
        """Get schema for a specific tool from either registry."""
        
        # First try to get from tool registry (BaseTool)
        tool = self.tool_registry.get_tool(tool_name)
        if tool:
            return self._convert_tool_to_function_schema(tool)
        
        # Then try to get from function executor
        schema = self.function_executor.registry.get_schema(tool_name)
        if schema:
            return schema
        
        return None
    
    def _convert_tool_to_function_schema(self, tool: BaseTool) -> Dict[str, Any]:
        """Convert a BaseTool to OpenAI function schema format."""
        tool_schema = tool.get_schema()
        
        return {
            "type": "function",
            "function": {
                "name": tool_schema.name,
                "description": tool_schema.description,
                "parameters": tool_schema.parameters
            }
        }
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool by name with given arguments.
        
        Args:
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            
        Returns:
            Execution result
        """
        # First try to execute as BaseTool
        tool = self.tool_registry.get_tool(tool_name)
        if tool:
            result = await tool.execute(**arguments)
            return {
                "success": result.success,
                "result": result.result,
                "error": result.error,
                "metadata": result.metadata
            }
        
        # Then try to execute as function
        import json
        function_result = await self.function_executor.execute_function(
            tool_name, 
            json.dumps(arguments)
        )
        
        return {
            "success": function_result.success,
            "result": function_result.result,
            "error": function_result.error,
            "execution_time": getattr(function_result, 'execution_time', None)
        }
    
    def validate_role_tools(self, tool_names: List[str]) -> Dict[str, Any]:
        """
        Validate that all tools required by a role are available.
        
        Args:
            tool_names: List of tool names to validate
            
        Returns:
            Validation result with available and missing tools
        """
        self.discover_and_load_tools()
        
        available_tools = self.get_available_tool_names()
        missing_tools = [name for name in tool_names if name not in available_tools]
        valid_tools = [name for name in tool_names if name in available_tools]
        
        return {
            "valid": len(missing_tools) == 0,
            "available_tools": valid_tools,
            "missing_tools": missing_tools,
            "total_requested": len(tool_names),
            "total_available": len(valid_tools)
        }


# Global tool manager instance
tool_manager = ToolManager()
